# Context
Filename: 指标目标页面布局调整任务.md
Created On: 2024-12-19
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
调整"当前指标目标"页面首页布局：
1. 右侧布局调整为上下结构
   - 上部分：现在的指标列表
   - 下部分：点选指标时显示tab分页，默认两个tab
2. Tab页面内容：
   - "采集表单" tab：展示 CitystandardItemForm.vue 的内容，使用 form-create 预览页展示，无数据时显示无数据页面，可以新增，新增弹出 CitystandardItemFormForm.vue
   - "分项指标" tab：预留，后续补充

# Project Overview
这是一个基于Vue3 + Element Plus的城市健康检查前端项目，使用了form-create表单设计器。当前指标目标页面位于 src/views/urban/citystandarditem/index.vue，采用左右布局，左侧是StandardSelector组件，右侧是指标列表。

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## 当前页面结构分析
- **主页面**: `src/views/urban/citystandarditem/index.vue`
  - 采用左右布局：左侧StandardSelector + 右侧指标列表
  - 右侧包含搜索表单和数据表格
  - 已有空状态提示逻辑（未选择标准体系时显示）

## 相关组件分析
- **CitystandardItemForm.vue**: 指标项的详细表单，采用向导式步骤设计
- **CitystandardItemFormForm.vue**: 采集表单配置弹窗，包含form-create设计器和预览功能
- **StandardSelector**: 标准体系选择器组件，支持搜索和分页

## 关键技术点
- **form-create预览**: CitystandardItemFormForm.vue中已实现预览模式，使用previewData.rule和previewData.option
- **空状态处理**: 项目中已有空状态组件模式，使用Icon + 文字描述
- **API接口**: CitystandardItemFormApi提供CRUD操作，支持根据itemId查询表单配置

## 数据流分析
- 指标列表通过CitystandardItemApi.getCitystandardItemPage获取
- 表单配置通过CitystandardItemFormApi.getCitystandardItemForm获取
- 需要建立指标ID与表单配置的关联关系

## 布局约束
- 需要保持左侧StandardSelector不变
- 右侧需要改为上下结构，上部分是指标列表，下部分是tab内容
- 需要处理响应式布局和高度计算

# Proposed Solution (Populated by INNOVATE mode)

# Implementation Plan (Generated by PLAN mode)

# Current Execution Step (Updated by EXECUTE mode when starting a step)

# Task Progress (Appended by EXECUTE mode after each step completion)

## 2024-12-19 执行记录

### 已完成的步骤：
1. ✅ 创建 IndicatorList.vue 组件 - 指标列表组件，包含搜索、分页、选择功能
2. ✅ 创建 IndicatorDetail.vue 组件 - 指标详情组件，包含tab切换和表单预览
3. ✅ 创建 FormPreview.vue 通用组件 - 表单预览组件，支持加载、错误、无数据状态
4. ✅ 修改主页面 index.vue 布局结构 - 改为上下分布，集成新组件
5. ✅ 实现组件导入和状态管理 - 添加selectedIndicator状态和相关方法
6. ✅ 修改样式布局 - 添加上下布局样式，50%高度分配
7. ✅ 修复API调用 - 使用分页接口获取表单配置
8. ✅ 修复表单弹窗参数传递 - 支持预设指标ID

### 核心功能实现：
- 🎯 上下布局：上部分显示指标列表，下部分显示详情tab
- 🎯 指标选择：点击指标行可选择，下方显示对应详情
- 🎯 表单预览：采集表单tab显示form-create预览
- 🎯 无数据处理：各种状态的无数据页面和操作引导
- 🎯 新增功能：可以为选中指标新增表单配置

### 待完善功能：
- 分项指标tab内容（已预留结构）
- 表单编辑功能集成
- 性能优化和错误处理完善

# Final Review (Populated by REVIEW mode)
