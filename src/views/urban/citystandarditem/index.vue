<template>
  <div class="citystandard-item-container">
    <!-- 左侧面板：标准体系选择器 -->
    <div class="left-panel">
      <StandardSelector
        ref="standardSelectorRef"
        :show-search="true"
        :show-pagination="true"
        :show-status="false"
        :table-height="'calc(100vh - 240px)'"
        :page-size="20"
        :enabled-only="true"
        @select="handleStandardSelect"
        @change="handleStandardChange"
      />
    </div>

    <!-- 右侧面板：上下布局 -->
    <div class="right-panel">
      <!-- 空状态提示 -->
      <div v-if="!selectedStandard" class="empty-state">
        <Icon icon="ep:document" size="48" color="#c0c4cc" />
        <p>请从左侧选择一个城市指标体系</p>
        <p class="empty-desc">选择标准体系后，可以查看和管理对应的指标目标</p>
      </div>

      <!-- 有选择标准体系时显示上下布局 -->
      <template v-else>
        <!-- 上部分：指标列表 -->
        <div class="indicator-list-section">
          <ContentWrap>
            <CitystandardItemList
              ref="indicatorListRef"
              :selected-standard="selectedStandard"
              @indicator-select="handleIndicatorSelect"
              @open-form="openForm"
              @delete-item="handleDelete"
              @export-data="handleExport"
            />
          </ContentWrap>
        </div>

        <!-- 下部分：指标详情 -->
        <div class="indicator-detail-section">
          <ContentWrap>
            <CitystandardItemDetail
              :selected-indicator="selectedIndicator"
            />
          </ContentWrap>
        </div>
      </template>

      <!-- 表单弹窗：添加/修改 -->
      <CitystandardItemForm ref="formRef" @success="getList" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { CitystandardItemApi, CitystandardItemVO } from '@/api/urban/citystandarditem'
import { CitystandardVO } from '@/api/urban/citystandard'
import CitystandardItemForm from './CitystandardItemForm.vue'
import { StandardSelector } from '@/components/StandardSelector'
import CitystandardItemList from './components/CitystandardItemList.vue'
import CitystandardItemDetail from './components/CitystandardItemDetail.vue'

/** 城市标准体体系-指标项表，用于记录标准体系下各类指标及其详细属性 列表 */
defineOptions({ name: 'CitystandardItem' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<CitystandardItemVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const selectedStandard = ref<CitystandardVO | null>(null) // 当前选中的标准体系
const selectedIndicator = ref<CitystandardItemVO | null>(null) // 当前选中的指标
const standardSelectorRef = ref() // 标准选择器引用
const indicatorListRef = ref() // 指标列表组件引用

const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  // 通过子组件刷新列表
  if (indicatorListRef.value) {
    indicatorListRef.value.getList()
  }
}

/** 处理标准体系选择 */
const handleStandardSelect = (standard: CitystandardVO) => {
  console.log('🎯 选择标准体系:', standard)
  selectedStandard.value = standard
}

/** 处理标准体系选择变化 */
const handleStandardChange = (standard: CitystandardVO | null) => {
  if (!standard) {
    selectedStandard.value = null
    selectedIndicator.value = null
    list.value = []
    total.value = 0
  }
}

/** 处理指标选择 */
const handleIndicatorSelect = (indicator: CitystandardItemVO) => {
  selectedIndicator.value = indicator
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  if (type === 'create' && !selectedStandard.value) {
    message.warning('请先选择城市指标体系')
    return
  }
  const standardId = selectedStandard.value?.id
  formRef.value.open(type, id, standardId)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CitystandardItemApi.deleteCitystandardItem(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const params = {
      citystandardId: selectedStandard.value?.id
    }
    const data = await CitystandardItemApi.exportCitystandardItem(params)
    download.excel(data, '指标目标.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  // 不自动加载列表，等待用户选择标准体系
})
</script>

<style lang="scss" scoped>
.citystandard-item-container {
  display: flex;
  height: calc(100vh - 120px);
  gap: 16px;

  .left-panel {
    width: 300px;
    display: flex;
    flex-direction: column;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    overflow: hidden;
    background: #fff;

    .panel-header {
      padding: 16px;
      background: #f8f9fa;
      border-bottom: 1px solid #e4e7ed;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
    }
  }

  .right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .empty-state {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #909399;

      p {
        margin: 8px 0;
        font-size: 16px;
      }

      .empty-desc {
        font-size: 14px;
        color: #c0c4cc;
      }
    }

    .indicator-list-section {
      flex: 0 0 50%;
      min-height: 500px;
      display: flex;
      flex-direction: column;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 0 16px 0;
        border-bottom: 1px solid #e4e7ed;
        margin-bottom: 16px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
          color: #303133;
        }

        .header-actions {
          display: flex;
          gap: 8px;
        }
      }
    }

    .indicator-detail-section {
      flex: 1;
      min-height: 400px;
      border-top: 1px solid #e4e7ed;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: #909399;

  p {
    margin: 16px 0 0 0;
    font-size: 16px;

    &.empty-desc {
      font-size: 14px;
      color: #c0c4cc;
      margin-top: 8px;
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 24px;
  border-top: 1px solid #e4e7ed;
  margin-top: 24px;
}

// 响应式设计
@media (max-width: 1200px) {
  .citystandard-item-container {
    flex-direction: column;
    height: auto;

    .left-panel {
      width: 100%;
      height: 400px;
    }

    .right-panel {
      height: auto;
    }
  }
}
</style>
