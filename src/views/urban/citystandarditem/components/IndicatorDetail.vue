<template>
  <div class="indicator-detail-container">
    <!-- 未选择指标时的提示 -->
    <div v-if="!selectedIndicator" class="empty-state">
      <Icon icon="ep:document" size="48" color="#c0c4cc" />
      <p>请从上方列表中选择一个指标</p>
      <p class="empty-desc">选择指标后，可以查看和管理对应的采集表单</p>
    </div>

    <!-- 选择指标后的详情内容 -->
    <div v-else class="detail-content">
      <!-- 指标基本信息 -->
      <div class="indicator-info">
        <h3>{{ selectedIndicator.itemName }}</h3>
        <p class="indicator-code">指标编号：{{ selectedIndicator.itemCode }}</p>
      </div>

      <!-- Tab 切换 -->
      <el-tabs v-model="activeTab" class="detail-tabs" tab-position="left">
        <!-- 采集表单 Tab -->
        <el-tab-pane label="采集表单" name="form">
          <div class="tab-content">
            <!-- 表单预览区域 -->
            <FormPreview
              :indicator-id="selectedIndicator.id"
              @add-form="handleAddForm"
            />
          </div>
        </el-tab-pane>

        <!-- 分项指标 Tab -->
        <el-tab-pane label="分项指标" name="subIndicators">
          <div class="tab-content">
            <div class="placeholder-content">
              <Icon icon="ep:setting" size="48" color="#c0c4cc" />
              <p>分项指标功能</p>
              <p class="placeholder-desc">此功能正在开发中，敬请期待</p>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 表单配置弹窗 -->
    <CitystandardItemFormForm 
      ref="formFormRef" 
      @success="handleFormSuccess" 
    />
  </div>
</template>

<script setup lang="ts">
import { CitystandardItemVO } from '@/api/urban/citystandarditem'
import FormPreview from '@/components/FormCreate/src/FormPreview.vue'
import CitystandardItemFormForm from '@/views/urban/citystandarditemform/CitystandardItemFormForm.vue'

/** 指标详情组件 */
defineOptions({ name: 'IndicatorDetail' })

// Props
interface Props {
  selectedIndicator: CitystandardItemVO | null
}

const props = defineProps<Props>()

// 响应式数据
const activeTab = ref('form')
const formFormRef = ref()

/** 处理新增表单 */
const handleAddForm = () => {
  if (props.selectedIndicator) {
    // 传递正确的参数：type, id, standardId, itemId
    formFormRef.value?.open('create', undefined, props.selectedIndicator.citystandardId, props.selectedIndicator.id)
  }
}

/** 处理表单操作成功 */
const handleFormSuccess = () => {
  // 刷新表单预览
  // FormPreview组件会自动监听selectedIndicator变化并刷新
}

// 监听选中指标变化，重置到表单tab
watch(() => props.selectedIndicator, (newIndicator) => {
  if (newIndicator) {
    activeTab.value = 'form'
  }
})
</script>

<style lang="scss" scoped>
.indicator-detail-container {
  height: 30vh;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 6px;
  overflow: hidden;

  .empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;
    
    p {
      margin: 8px 0;
      font-size: 16px;
    }
    
    .empty-desc {
      font-size: 14px;
      color: #c0c4cc;
    }
  }

  .detail-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .indicator-info {
      padding: 16px 20px;
      border-bottom: 1px solid #e4e7ed;
      background: #f8f9fa;

      h3 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 500;
        color: #303133;
      }

      .indicator-code {
        margin: 0;
        font-size: 14px;
        color: #606266;
      }
    }

    .detail-tabs {
      flex: 1;
      display: flex;
      flex-direction: row; // 改为水平布局

      :deep(.el-tabs__header) {
        margin: 0;
        width: 120px; // 固定tab头部宽度
        flex-shrink: 0;
      }

      :deep(.el-tabs__nav-wrap) {
        padding: 0;
      }

      :deep(.el-tabs__content) {
        flex: 1;
        padding: 0;
        margin: 0;
      }

      :deep(.el-tab-pane) {
        height: 100%;
      }

      .tab-content {
        height: 100%;
        padding: 20px;

        .placeholder-content {
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: #909399;

          p {
            margin: 8px 0;
            font-size: 16px;
          }

          .placeholder-desc {
            font-size: 14px;
            color: #c0c4cc;
          }
        }
      }
    }
  }
}
</style>
