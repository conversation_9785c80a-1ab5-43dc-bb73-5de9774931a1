<template>
  <div class="indicator-list-container">
    <!-- 搜索表单 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="search-form"
      label-width="68px"
    >
      <el-form-item label="指标项" prop="itemName">
        <el-input
          v-model="queryParams.itemName"
          placeholder="请输入指标项"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="指标编号" prop="itemCode">
        <el-input
          v-model="queryParams.itemCode"
          placeholder="请输入指标编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery" type="primary">
          <Icon icon="ep:search" class="mr-5px" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="list"
        :stripe="true"
        :show-overflow-tooltip="true"
        :highlight-current-row="true"
        @current-change="handleRowClick"
        :height="400"
      >
      <el-table-column label="序号" align="center" prop="seqNo" width="80" />
      <el-table-column label="一级维度" prop="dimensionLevel1" width="120" show-overflow-tooltip />
      <el-table-column label="二级维度" prop="dimensionLevel2" width="120" show-overflow-tooltip />
      <el-table-column label="指标项" prop="itemName" min-width="150" show-overflow-tooltip />
      <el-table-column label="指标编号" prop="itemCode" width="120" show-overflow-tooltip />
      <el-table-column label="核心指标" align="center" prop="isCore" width="90">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.UC_YESORNO" :value="scope.row.isCore" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['urban:citystandard-item:update']"
            size="small"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['urban:citystandard-item:delete']"
            size="small"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        v-if="selectedStandard"
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { DICT_TYPE } from '@/utils/dict'
import { CitystandardItemApi, CitystandardItemVO } from '@/api/urban/citystandarditem'
import { CitystandardVO } from '@/api/urban/citystandard'

/** 指标列表组件 */
defineOptions({ name: 'IndicatorList' })

// Props
interface Props {
  selectedStandard: CitystandardVO | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'indicator-select': [indicator: CitystandardItemVO]
  'open-form': [type: string, id?: number]
  'delete-item': [id: number]
}>()

// 响应式数据
const loading = ref(false)
const list = ref<CitystandardItemVO[]>([])
const total = ref(0)
const queryFormRef = ref()

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  citystandardId: undefined as number | undefined,
  itemName: undefined,
  itemCode: undefined
})

/** 查询列表 */
const getList = async () => {
  if (!props.selectedStandard) return

  loading.value = true
  try {
    const data = await CitystandardItemApi.getCitystandardItemPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 监听选中的标准体系变化
watch(() => props.selectedStandard, (newStandard) => {
  if (newStandard) {
    queryParams.citystandardId = newStandard.id
    queryParams.pageNo = 1
    getList()
  } else {
    list.value = []
    total.value = 0
  }
}, { immediate: true })

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 处理行点击 */
const handleRowClick = (row: CitystandardItemVO) => {
  if (row) {
    emit('indicator-select', row)
  }
}

/** 打开表单 */
const openForm = (type: string, id?: number) => {
  emit('open-form', type, id)
}

/** 删除操作 */
const handleDelete = (id: number) => {
  emit('delete-item', id)
}

// 暴露方法供父组件调用
defineExpose({
  getList
})
</script>

<style lang="scss" scoped>
.indicator-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;

  .search-form {
    margin-bottom: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    flex-shrink: 0;
  }

  .table-container {
    flex: 1;
    display: flex;
    flex-direction: column;

    .el-table {
      margin-bottom: 16px;

      // 表格行鼠标样式
      :deep(.el-table__row) {
        cursor: pointer;
      }
    }

    // 分页样式
    :deep(.el-pagination) {
      justify-content: center;
      flex-shrink: 0;
    }
  }
}
</style>
