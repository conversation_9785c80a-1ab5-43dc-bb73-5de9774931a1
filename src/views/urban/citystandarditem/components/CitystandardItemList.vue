<template>
  <div class="indicator-list-container">
    <!-- 搜索表单 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="search-form"
      label-width="68px"
    >
      <el-form-item label="指标项" prop="itemName">
        <el-input
          v-model="queryParams.itemName"
          placeholder="请输入指标项"
          clearable
          @keyup.enter="handleQuery"
          class="!w-180px"
        />
      </el-form-item>
      <el-form-item label="指标编号" prop="itemCode">
        <el-input
          v-model="queryParams.itemCode"
          placeholder="请输入指标编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-180px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery" type="primary" size="small">
          <Icon icon="ep:search" class="mr-5px" />
          搜索
        </el-button>
        <el-button @click="resetQuery" size="small">
          <Icon icon="ep:refresh" class="mr-5px" />
          重置
        </el-button>
        <el-button @click="showAdvancedSearch = true" size="small">
          <Icon icon="ep:operation" class="mr-5px" />
          高级查询
        </el-button>
        <el-button
          type="primary"
          @click="handleCreate"
          v-hasPermi="['urban:citystandard-item:create']"
          size="small"
        >
          <Icon icon="ep:plus" class="mr-5px" />
          新增指标
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          v-hasPermi="['urban:citystandard-item:export']"
          size="small"
        >
          <Icon icon="ep:download" class="mr-5px" />
          导出
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="list"
        :stripe="true"
        :show-overflow-tooltip="true"
        :highlight-current-row="true"
        @current-change="handleRowClick"
        :height="330"
      >
      <el-table-column label="序号" align="center" prop="seqNo" width="80" />
      <el-table-column label="一级维度" prop="dimensionLevel1" width="120" show-overflow-tooltip />
      <el-table-column label="二级维度" prop="dimensionLevel2" width="120" show-overflow-tooltip />
      <el-table-column label="指标项" prop="itemName" min-width="150" show-overflow-tooltip />
      <el-table-column label="指标编号" prop="itemCode" width="120" show-overflow-tooltip />
      <el-table-column label="核心指标" align="center" prop="isCore" width="90">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.UC_YESORNO" :value="scope.row.isCore" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['urban:citystandard-item:update']"
            size="small"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['urban:citystandard-item:delete']"
            size="small"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        v-if="selectedStandard"
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 高级查询抽屉 -->
    <el-drawer
      v-model="showAdvancedSearch"
      title="高级查询"
      :size="400"
      direction="rtl"
    >
      <el-form :model="queryParams" label-width="100px">
        <el-form-item label="序号" prop="seqNo">
          <el-input
            v-model="queryParams.seqNo"
            placeholder="请输入序号"
            clearable
          />
        </el-form-item>
        <el-form-item label="国家基础指标" prop="isNational">
          <el-select
            v-model="queryParams.isNational"
            placeholder="请选择国家基础指标"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="地方特色指标" prop="isLocal">
          <el-select
            v-model="queryParams.isLocal"
            placeholder="请选择地方特色指标"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="小程序采集" prop="isApplet">
          <el-select
            v-model="queryParams.isApplet"
            placeholder="请选择是否小程序采集"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.UC_YESORNO)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="一级维度" prop="dimensionLevel1">
          <el-input
            v-model="queryParams.dimensionLevel1"
            placeholder="请输入一级维度"
            clearable
          />
        </el-form-item>
        <el-form-item label="二级维度" prop="dimensionLevel2">
          <el-input
            v-model="queryParams.dimensionLevel2"
            placeholder="请输入二级维度"
            clearable
          />
        </el-form-item>
        <el-form-item label="指标项别名" prop="itemAlias">
          <el-input
            v-model="queryParams.itemAlias"
            placeholder="请输入指标项别名"
            clearable
          />
        </el-form-item>
        <el-form-item label="数据来源" prop="dataSource">
          <el-input
            v-model="queryParams.dataSource"
            placeholder="请输入数据来源"
            clearable
          />
        </el-form-item>
        <el-form-item label="责任部门" prop="responsibleDept">
          <el-input
            v-model="queryParams.responsibleDept"
            placeholder="请输入责任部门"
            clearable
          />
        </el-form-item>

        <div class="drawer-footer">
          <el-button @click="handleAdvancedQuery">确认查询</el-button>
          <el-button @click="resetAdvancedQuery">清空条件</el-button>
          <el-button @click="showAdvancedSearch = false">取消</el-button>
        </div>
      </el-form>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { CitystandardItemApi, CitystandardItemVO } from '@/api/urban/citystandarditem'
import { CitystandardVO } from '@/api/urban/citystandard'

/** 指标列表组件 */
defineOptions({ name: 'IndicatorList' })

// Props
interface Props {
  selectedStandard: CitystandardVO | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'indicator-select': [indicator: CitystandardItemVO]
  'open-form': [type: string, id?: number]
  'delete-item': [id: number]
  'export-data': []
}>()

// 响应式数据
const loading = ref(false)
const list = ref<CitystandardItemVO[]>([])
const total = ref(0)
const queryFormRef = ref()
const showAdvancedSearch = ref(false) // 高级查询抽屉显示状态

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  citystandardId: undefined as number | undefined,
  seqNo: undefined,
  isCore: undefined,
  isNational: undefined,
  isLocal: undefined,
  isApplet: undefined,
  dimensionLevel1: undefined,
  dimensionLevel2: undefined,
  itemName: undefined,
  itemAlias: undefined,
  itemCode: undefined,
  explanation: undefined,
  evaluationCriteria: undefined,
  dataDecomposition: undefined,
  dataFormat: undefined,
  collectionContent: undefined,
  dataSource: undefined,
  responsibleDept: undefined,
  caseDemo: undefined,
  createTime: []
})

/** 查询列表 */
const getList = async () => {
  if (!props.selectedStandard) return

  loading.value = true
  try {
    const data = await CitystandardItemApi.getCitystandardItemPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 监听选中的标准体系变化
watch(() => props.selectedStandard, (newStandard) => {
  if (newStandard) {
    queryParams.citystandardId = newStandard.id
    queryParams.pageNo = 1
    getList()
  } else {
    list.value = []
    total.value = 0
  }
}, { immediate: true })

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 处理行点击 */
const handleRowClick = (row: CitystandardItemVO) => {
  if (row) {
    emit('indicator-select', row)
  }
}

/** 打开表单 */
const openForm = (type: string, id?: number) => {
  emit('open-form', type, id)
}

/** 新增操作 */
const handleCreate = () => {
  emit('open-form', 'create')
}

/** 导出操作 */
const handleExport = () => {
  emit('export-data')
}

/** 删除操作 */
const handleDelete = (id: number) => {
  emit('delete-item', id)
}

/** 高级查询确认 */
const handleAdvancedQuery = () => {
  queryParams.pageNo = 1
  getList()
  showAdvancedSearch.value = false
}

/** 重置高级查询条件 */
const resetAdvancedQuery = () => {
  // 重置高级查询条件，保留基础查询条件
  queryParams.seqNo = undefined
  queryParams.isCore = undefined
  queryParams.isNational = undefined
  queryParams.isLocal = undefined
  queryParams.isApplet = undefined
  queryParams.dimensionLevel1 = undefined
  queryParams.dimensionLevel2 = undefined
  queryParams.itemAlias = undefined
  queryParams.explanation = undefined
  queryParams.evaluationCriteria = undefined
  queryParams.dataDecomposition = undefined
  queryParams.dataFormat = undefined
  queryParams.collectionContent = undefined
  queryParams.dataSource = undefined
  queryParams.responsibleDept = undefined
  queryParams.caseDemo = undefined
  queryParams.createTime = []
}

// 暴露方法供父组件调用
defineExpose({
  getList
})
</script>

<style lang="scss" scoped>
.indicator-list-container {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .search-form {
    margin-bottom: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    flex-shrink: 0;

    // 确保表单项在一行显示
    :deep(.el-form-item) {
      margin-bottom: 0;
      margin-right: 16px;
    }

    // 最后一个表单项不需要右边距
    :deep(.el-form-item:last-child) {
      margin-right: 0;
    }
  }

  .table-container {
    flex: 1;
    display: flex;
    flex-direction: column;

    .el-table {
      margin-bottom: 16px;

      // 表格行鼠标样式
      :deep(.el-table__row) {
        cursor: pointer;
      }
    }

    // 分页样式
    :deep(.el-pagination) {
      justify-content: center;
      flex-shrink: 0;
    }
  }
}

// 抽屉底部按钮样式
.drawer-footer {
  display: flex;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
  margin-top: 20px;
}
</style>
