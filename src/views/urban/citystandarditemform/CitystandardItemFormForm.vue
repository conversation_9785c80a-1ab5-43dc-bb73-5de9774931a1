<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1500px" :close-on-click-modal="false">
    <div class="form-layout-container">
      <!-- 左侧：基本信息表单 -->
      <div class="left-form-panel">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          v-loading="formLoading"
        >
          <el-form-item label="城市指标体系" prop="citystandardId">
            <el-select
              v-model="formData.citystandardId"
              placeholder="请选择城市指标体系"
              clearable
              filterable
              style="width: 100%"
              @change="handleStandardChange"
            >
              <el-option
                v-for="item in citystandardList"
                :key="item.id"
                :label="`${item.name} (${item.publishYear})`"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="指标项" prop="itemId">
            <el-select
              v-model="formData.itemId"
              placeholder="请选择指标项"
              clearable
              filterable
              style="width: 100%"
              :disabled="!formData.citystandardId"
            >
              <el-option
                v-for="item in citystandardItemList"
                :key="item.id"
                :label="`${item.itemName} (${item.itemCode})`"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              placeholder="请输入备注"
              type="textarea"
              :rows="3"
            />
          </el-form-item>

          <el-form-item label="版本" prop="formVersion">
            <el-input-number
              v-model="formData.formVersion"
              placeholder="请输入版本"
              :min="1"
              style="width: 100%"
            />
          </el-form-item>

          <!-- 表单配置状态显示 -->
          <el-divider content-position="left">配置状态</el-divider>

          <el-form-item label="表单组件配置">
            <el-tag :type="hasFormConfig ? 'success' : 'info'">
              {{ hasFormConfig ? '已配置' : '未配置' }}
            </el-tag>
          </el-form-item>
        </el-form>
      </div>

      <!-- 右侧：表单配置区域 -->
      <div class="right-config-panel">
        <div class="form-config-container">
          <div class="config-toolbar">
            <div class="toolbar-title">
              <Icon icon="ep:setting" class="mr-5px" />
              表单组件配置
            </div>
            <el-button-group>
              <el-button
                :type="designMode === 'design' ? 'primary' : ''"
                @click="designMode = 'design'"
                size="small"
              >
                <Icon icon="ep:edit" class="mr-5px" />
                设计模式
              </el-button>
              <el-button
                :type="designMode === 'preview' ? 'primary' : ''"
                @click="designMode = 'preview'"
                :disabled="!hasFormConfig"
                size="small"
              >
                <Icon icon="ep:view" class="mr-5px" />
                预览模式
              </el-button>
            </el-button-group>
          </div>

          <!-- 设计器模式 -->
          <div v-show="designMode === 'design'" class="designer-container">
            <fc-designer ref="designer" :config="designerConfig" height="450px" />
          </div>

          <!-- 预览模式 -->
          <div v-show="designMode === 'preview'" class="preview-container">
            <form-create
              v-if="hasFormConfig"
              :rule="previewData.rule"
              :option="previewData.option"
            />
            <div v-else class="no-config-tip">
              <Icon icon="ep:warning" size="48" color="#e6a23c" />
              <p>暂无表单配置，请先在设计模式中设计表单</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { CitystandardItemFormApi, CitystandardItemFormVO } from '@/api/urban/citystandarditemform'
import { CitystandardApi, CitystandardVO } from '@/api/urban/citystandard'
import { CitystandardItemApi, CitystandardItemVO } from '@/api/urban/citystandarditem'
import { useFormCreateDesigner } from '@/components/FormCreate'
import formCreate from '@form-create/element-ui'

/** 指标目标采集表单 表单 */
defineOptions({ name: 'CitystandardItemFormForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const designMode = ref('design') // 设计模式：design | preview

// 下拉选择数据
const citystandardList = ref<CitystandardVO[]>([]) // 城市指标体系列表
const citystandardItemList = ref<CitystandardItemVO[]>([]) // 指标项列表


// 设计器相关
const designer = ref() // 设计器引用
const designerInitialized = ref(false) // 设计器是否已初始化

// 获取设计器增强功能（需要等待async函数完成）
let initFormCreateDesigner: (() => Promise<void>) | null = null

// 初始化useFormCreateDesigner
const setupFormCreateDesigner = async () => {
  const result = await useFormCreateDesigner(designer)
  initFormCreateDesigner = result.init
}
// 表单设计器配置
const designerConfig = ref({
  switchType: [], // 是否可以切换组件类型,或者可以相互切换的字段
  autoActive: true, // 是否自动选中拖入的组件
  useTemplate: false, // 是否生成vue2语法的模板组件
  formOptions: {
    form: {
      labelWidth: '100px' // 设置默认的 label 宽度为 100px
    }
  }, // 定义表单配置默认值
  fieldReadonly: false, // 配置field是否可以编辑
  hiddenDragMenu: false, // 隐藏拖拽操作按钮
  hiddenDragBtn: false, // 隐藏拖拽按钮
  hiddenMenu: [], // 隐藏部分菜单
  hiddenItem: [], // 隐藏部分组件
  hiddenItemConfig: {}, // 隐藏组件的部分配置项
  disabledItemConfig: {}, // 禁用组件的部分配置项
  showSaveBtn: false, // 是否显示保存按钮
  showConfig: true, // 是否显示右侧的配置界面
  showBaseForm: true, // 是否显示组件的基础配置表单
  showControl: true, // 是否显示组件联动
  showPropsForm: true, // 是否显示组件的属性配置表单
  showEventForm: true, // 是否显示组件的事件配置表单
  showValidateForm: true, // 是否显示组件的验证配置表单
  showFormConfig: true, // 是否显示表单配置
  showInputData: true, // 是否显示录入按钮
  showDevice: true, // 是否显示多端适配选项
  appendConfigData: [] // 定义渲染规则所需的formData
})

// 表单数据
const formData = ref({
  id: undefined,
  itemId: undefined,
  citystandardId: undefined,
  formRule: undefined,
  formOption: undefined,
  remark: undefined,
  formVersion: 1
})

// 预览数据
const previewData = ref({
  formData: {},
  rule: [],
  option: {
    submitBtn: false,
    resetBtn: false
  }
})


const formRules = reactive({
  itemId: [{ required: true, message: '指标项不能为空', trigger: 'change' }],
  citystandardId: [{ required: true, message: '城市指标体系不能为空', trigger: 'change' }],
  formRule: [{ required: true, message: '表单组件配置不能为空', trigger: 'blur' }],
  formOption: [{ required: true, message: '表单全局配置不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

// 初始化设计器增强功能
const initDesigner = async () => {
  if (designerInitialized.value) {
    console.log('⚠️ 设计器已经初始化过了，跳过')
    return
  }

  if (!designer.value) {
    console.log('⚠️ 设计器引用不存在，跳过初始化')
    return
  }

  if (!dialogVisible.value) {
    console.log('⚠️ 弹窗未显示，跳过初始化')
    return
  }

  try {
    console.log('🎯 开始初始化FormCreate设计器增强功能...')
    console.log('设计器引用:', designer.value)
    console.log('弹窗状态:', dialogVisible.value)

    // 确保useFormCreateDesigner已经设置完成
    if (!initFormCreateDesigner) {
      console.log('📦 正在设置useFormCreateDesigner...')
      await setupFormCreateDesigner()
    }

    // 调用useFormCreateDesigner返回的初始化方法
    if (initFormCreateDesigner) {
      await initFormCreateDesigner()
      designerInitialized.value = true
      console.log('✅ FormCreate设计器增强功能初始化完成')
    } else {
      console.error('❌ initFormCreateDesigner未正确设置')
    }
  } catch (error) {
    console.error('❌ FormCreate设计器增强功能初始化失败:', error)
  }
}



// 计算属性
const hasFormConfig = computed(() => {
  if (!designer.value) return false
  try {
    const rule = designer.value.getRule()
    return rule && rule.length > 0
  } catch (error) {
    return false
  }
})

const hasOptionConfig = computed(() => {
  if (!designer.value) return false
  try {
    const option = designer.value.getOption()
    // 检查是否有实际的配置内容（排除默认的空配置）
    return option && Object.keys(option).length > 0 &&
           JSON.stringify(option) !== JSON.stringify(designerConfig.value.formOptions || {})
  } catch (error) {
    return false
  }
})

// 监听设计器DOM和弹窗状态，自动初始化设计器
watch([designer, dialogVisible], async ([designerRef, visible]) => {
  if (designerRef && visible && !designerInitialized.value) {
    // 等待DOM完全渲染
    await nextTick()
    // 再次检查设计器DOM是否真正存在
    if (designerRef.$el || designerRef.value) {
      await initDesigner()
    }
  }
}, { immediate: true })

// 监听设计模式变化，更新预览数据
watch(designMode, (newMode) => {
  if (newMode === 'preview' && designer.value) {
    updatePreviewData()
  }
})

// 更新预览数据
const updatePreviewData = () => {
  if (designer.value) {
    try {
      const rule = designer.value.getRule()
      const option = designer.value.getOption()

      previewData.value.rule = rule
      previewData.value.option = {
        ...option,
        submitBtn: false,
        resetBtn: false
      }

      // 更新表单数据
      formData.value.formRule = rule
      formData.value.formOption = option
    } catch (error) {
      console.error('更新预览数据失败:', error)
      message.error('表单配置格式错误')
    }
  }
}



// 加载城市指标体系列表
const loadCitystandardList = async () => {
  try {
    const data = await CitystandardApi.getCitystandardList({
      enabled: 1 // 只加载启用的
    })
    citystandardList.value = data
  } catch (error) {
    console.error('加载城市指标体系列表失败:', error)
  }
}

// 加载指标项列表
const loadCitystandardItemList = async (citystandardId: number) => {
  if (!citystandardId) {
    citystandardItemList.value = []
    return
  }

  try {
    const data = await CitystandardItemApi.getCitystandardItemList({
      citystandardId: citystandardId
    })
    citystandardItemList.value = data
  } catch (error) {
    console.error('加载指标项列表失败:', error)
  }
}

// 处理指标体系变化
const handleStandardChange = (citystandardId: number) => {
  formData.value.itemId = undefined // 清空指标项选择
  loadCitystandardItemList(citystandardId)
}

/** 打开弹窗 */
const open = async (type: string, id?: number, standardId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 加载基础数据
  await loadCitystandardList()

  // 如果传入了标准体系ID，设置默认值
  if (standardId && type === 'create') {
    formData.value.citystandardId = standardId
    await loadCitystandardItemList(standardId)
  }

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await CitystandardItemFormApi.getCitystandardItemForm(id)
      formData.value = data

      // 加载对应的指标项列表
      if (data.citystandardId) {
        await loadCitystandardItemList(data.citystandardId)
      }

      // 设置设计器数据（延迟执行，确保设计器已初始化）
      if (data.formRule) {
        setTimeout(async () => {
          if (designer.value) {
            try {
              const rule = Array.isArray(data.formRule) ? data.formRule : JSON.parse(data.formRule)
              const option = data.formOption || {}

              // 设置设计器的规则和选项
              designer.value.setRule(rule)
              designer.value.setOption(option)

              // 设置预览数据
              previewData.value.rule = rule
              previewData.value.option = {
                ...option,
                submitBtn: false,
                resetBtn: false
              }
            } catch (error) {
              console.error('设置设计器数据失败:', error)
            }
          }
        }, 200)
      }



    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  // 更新表单配置数据
  if (designer.value) {
    try {
      formData.value.formRule = designer.value.getRule()
      formData.value.formOption = designer.value.getOption()
    } catch (error) {
      console.error('获取设计器数据失败:', error)
      message.error('获取表单配置失败，请检查表单设计')
      return
    }
  }

  // 提交请求
  formLoading.value = true
  try {
    const data = {
      ...formData.value
    } as unknown as CitystandardItemFormVO

    if (formType.value === 'create') {
      await CitystandardItemFormApi.createCitystandardItemForm(data)
      message.success(t('common.createSuccess'))
    } else {
      await CitystandardItemFormApi.updateCitystandardItemForm(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    itemId: undefined,
    citystandardId: undefined,
    formRule: undefined,
    formOption: undefined,
    remark: undefined,
    formVersion: 1
  }

  // 重置其他状态
  designMode.value = 'design'
  citystandardItemList.value = []
  designerInitialized.value = false // 重置设计器初始化状态

  // 重置预览数据
  previewData.value = {
    formData: {},
    rule: [],
    option: {
      submitBtn: false,
      resetBtn: false
    }
  }

  // 重置设计器
  if (designer.value) {
    try {
      designer.value.setRule([])
      designer.value.setOption({})
    } catch (error) {
      console.error('重置设计器失败:', error)
    }
  }

  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.form-layout-container {
  display: flex;
  gap: 20px;
  height: 600px;

  .left-form-panel {
    width: 350px;
    flex-shrink: 0;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    padding: 20px;
    background: #fafafa;
    overflow-y: auto;

    .el-form {
      height: 100%;
    }

    .el-form-item {
      margin-bottom: 20px;
    }
  }

  .right-config-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    overflow: hidden;
  }
}

.form-config-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .config-toolbar {
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .toolbar-title {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      display: flex;
      align-items: center;
    }
  }

  .designer-container {
    flex: 1;
    padding: 16px;
    overflow: hidden;
  }

  .preview-container {
    flex: 1;
    padding: 16px;
    background: #fafafa;
    overflow-y: auto;
  }



  .no-config-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #909399;

    p {
      margin: 16px 0 0 0;
      font-size: 14px;
    }
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
  height: 600px;
  overflow: hidden;
}

// 响应式设计
@media (max-width: 1200px) {
  .form-layout-container {
    flex-direction: column;
    height: auto;

    .left-form-panel {
      width: 100%;
      height: auto;
      max-height: 300px;
    }

    .right-config-panel {
      height: 400px;
    }
  }
}
</style>
